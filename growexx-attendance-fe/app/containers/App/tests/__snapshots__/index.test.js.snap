// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<App /> should render and match the snapshot 1`] = `
<ForwardRef(App__AppWrapper)
  data-testid="AppRoutes"
>
  <HelmetWrapper
    defaultTitle="Growexx Timesheet System"
    defer={true}
    encodeSpecialCharacters={true}
    titleTemplate="%s - Growexx Timesheet System"
  >
    <meta
      content="A Growexx Timesheet System "
      name="description"
    />
    <link
      href="IMAGE_MOCK"
      rel="icon"
      sizes="512x512"
      type="image/png"
    />
    <link
      href="IMAGE_MOCK"
      rel="icon"
      sizes="192x192"
      type="image/png"
    />
    <link
      href="IMAGE_MOCK"
      rel="icon"
      sizes="32x32"
      type="image/png"
    />
    <link
      href="IMAGE_MOCK"
      rel="icon"
      sizes="16x16"
      type="image/png"
    />
    <link
      href="IMAGE_MOCK"
      rel="apple-touch-icon"
    />
  </HelmetWrapper>
  <Switch>
    <DefaultRoute
      exact={true}
      path="/"
    />
    <withRouter(RoleMiddleWare)
      component={[Function]}
      exact={true}
      path="/connected-jira"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/users"
    />
    <PrivateRoute
      component={[Function]}
      path="/projects"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/assign-kra"
      showError={true}
    />
    <withRouter(RoleMiddleWare)
      component={[Function]}
      path="/start-assessment"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/assigned-kra"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/bulk-kra-assignment"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/stock-kra"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/edit-kra"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/assign-user-kra/detail"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      exact={true}
      path="/assessment/reporting-manager"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      exact={true}
      path="/assessment/review-manager"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/assessment/view/:id"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/assessment/rm-view/:id"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/assessment/rate/:id"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/self-kra/view/:id"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/self-kra/rate/:id"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/self-kra"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/assessment/self-comment"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/category-weightage-master"
      showError={true}
    />
    <withRouter(RoleMiddleWare)
      component={[Function]}
      exact={true}
      path="/rag-report"
      showError={true}
    />
    <withRouter(RoleMiddleWare)
      component={[Function]}
      exact={true}
      path="/project-tracker"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/change-password"
    />
    <PrivateRoute
      component={[Function]}
      path="/logs"
    />
    <PrivateRoute
      component={[Function]}
      path="/review-logs"
    />
    <PrivateRoute
      component={[Function]}
      path="/logout"
    />
    <AuthRoute
      component={[Function]}
      exact={true}
      path="/login"
    />
    <Route
      component={
        Object {
          "$$typeof": Symbol(react.memo),
          "WrappedComponent": [Function],
          "compare": null,
          "displayName": "Connect(PLIRedirect)",
          "type": [Function],
        }
      }
      exact={true}
      path="/pli/employee-profile-redirect"
    />
    <Route
      component={
        Object {
          "$$typeof": Symbol(react.memo),
          "WrappedComponent": [Function],
          "compare": null,
          "displayName": "Connect(EmployeeProfile)",
          "type": [Function],
        }
      }
      exact={true}
      path="/my-pli-rating"
    />
    <Route
      component={[Function]}
      exact={true}
      path="/bulk-action"
    />
    <AuthRoute
      component={[Function]}
      exact={true}
      path="/forgot-password"
    />
    <AuthRoute
      component={[Function]}
      exact={true}
      path="/reset-password/:id"
    />
    <Route
      component={[Function]}
      exact={true}
      path="/403"
    />
    <PrivateRoute
      component={[Function]}
      exact={true}
      path="/mentee"
    />
    <PrivateRoute
      component={
        Object {
          "$$typeof": Symbol(react.memo),
          "WrappedComponent": [Function],
          "compare": null,
          "displayName": "Connect(EmployeeProfile)",
          "type": [Function],
        }
      }
      path="/pli/employee-profile/:employeeId"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/pli-ratings-overview"
      showError={true}
    />
    <PrivateRoute
      component={[Function]}
      path="/parameter-configuration"
    />
    <PrivateRoute
      component={[Function]}
      showError={true}
    />
    <Route
      component={[Function]}
      path=""
    />
  </Switch>
  <GlobalStyleComponent />
</ForwardRef(App__AppWrapper)>
`;
