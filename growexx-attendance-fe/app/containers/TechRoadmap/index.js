/**
 * TechRoadmap
 */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Form, Input, Button, Row, Col, Select, message, Modal } from 'antd';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';

const { Option } = Select;
const { TextArea } = Input;

const TechRoadmap = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const roleOptions = [
    { label: 'Developers', value: 'Developers' },
    { label: 'PO', value: 'PO' },
    { label: 'TPM', value: 'TPM' },
    { label: 'PM', value: 'PM' },
    { label: 'TL', value: 'TL' },
    { label: 'QA', value: 'QA' },
  ];

  const onFinish = async values => {
    try {
      setLoading(true);

      // Format the duration to include 'Months'
      const formattedValues = {
        ...values,
        duration: `${values.duration} Months`,
      };

      const response = await request(API_ENDPOINTS.TECH_ROADMAP_COURSE, {
        method: 'POST',
        body: formattedValues,
      });
      if (response && response.status === 200) {
        message.success('Course added successfully');
        form.resetFields();
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      message.error(error.response?.message || 'Failed to add course');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="Add a new course"
      visible={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          duration: '3 Months',
        }}
        style={{ width: '100%' }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="courseName"
              label={
                <span style={{ fontWeight: '500', color: '#333' }}>
                  Course Name
                </span>
              }
              rules={[
                {
                  required: true,
                  message: 'Please enter course name',
                },
              ]}
            >
              <Input
                placeholder="Course Name"
                style={{
                  borderRadius: '4px',
                  height: '40px',
                  borderColor: '#d9d9d9',
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="description"
              label={
                <span style={{ fontWeight: '500', color: '#333' }}>
                  Description
                </span>
              }
              rules={[
                {
                  required: true,
                  message: 'Please enter description',
                },
              ]}
            >
              <TextArea
                placeholder="Description"
                rows={4}
                style={{
                  borderRadius: '4px',
                  borderColor: '#d9d9d9',
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="duration"
              label={
                <span style={{ fontWeight: '500', color: '#333' }}>
                  Course duration
                </span>
              }
              rules={[
                {
                  required: true,
                  message: 'Please enter course duration',
                },
                {
                  validator: (_, value) => {
                    const numValue = Number(value);
                    if (isNaN(numValue) || numValue < 1 || numValue > 12) {
                      return Promise.reject(
                        'Duration must be between 1 and 12 months',
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  maxWidth: '250px',
                }}
              >
                <Input
                  type="number"
                  min={1}
                  max={12}
                  placeholder="Duration"
                  style={{
                    borderRadius: '4px',
                    height: '40px',
                    borderColor: '#d9d9d9',
                    width: '120px',
                  }}
                />
                <div
                  style={{
                    marginLeft: '8px',
                    fontSize: '14px',
                    width: '80px',
                    padding: '0 8px',
                    height: '40px',
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                  }}
                >
                  Months
                </div>
              </div>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="learningMedium"
              label={
                <span style={{ fontWeight: '500', color: '#333' }}>
                  Learning Medium
                </span>
              }
              rules={[
                {
                  required: true,
                  message: 'Please enter learning medium',
                },
              ]}
            >
              <Input
                placeholder="Learning Medium"
                style={{
                  borderRadius: '4px',
                  height: '40px',
                  borderColor: '#d9d9d9',
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="role"
              label={
                <span style={{ fontWeight: '500', color: '#333' }}>Role</span>
              }
              rules={[
                {
                  required: true,
                  message: 'Please select a role',
                },
              ]}
            >
              <Select
                placeholder="Select a role"
                style={{
                  width: '100%',
                  borderRadius: '4px',
                  height: '40px',
                }}
                dropdownStyle={{ borderRadius: '4px' }}
              >
                {roleOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button
              type="default"
              style={{
                marginRight: 8,
                color: '#fff',
                borderColor: '#4d186e',
                background: '#4d186e',
                textShadow: '0 -1px 0 rgba(0, 0, 0, 0.12)',
                boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)',
                minWidth: '120px',
                height: '40px',
                borderRadius: '4px',
                fontWeight: '500',
              }}
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{
                color: '#fff',
                borderColor: '#4d186e',
                background: '#4d186e',
                textShadow: '0 -1px 0 rgba(0, 0, 0, 0.12)',
                boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)',
                minWidth: '120px',
                height: '40px',
                borderRadius: '4px',
                fontWeight: '500',
              }}
            >
              Submit
            </Button>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

TechRoadmap.propTypes = {
  visible: PropTypes.bool.isRequired,
  onCancel: PropTypes.func.isRequired,
  onSuccess: PropTypes.func,
};

export default TechRoadmap;
